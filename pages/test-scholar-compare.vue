<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Scholar 比较过境页面模式测试</h1>
    
    <div class="space-y-4">
      <div>
        <h2 class="text-lg font-semibold mb-2">测试场景 1: 模拟从 Compare 页面重定向</h2>
        <button 
          @click="testTransitPageFlow"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          测试过境页面流程
        </button>
      </div>
      
      <div>
        <h2 class="text-lg font-semibold mb-2">测试场景 2: 直接访问 Scholar_Compare 页面</h2>
        <button 
          @click="testDirectAccess"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          测试直接访问
        </button>
      </div>
      
      <div>
        <h2 class="text-lg font-semibold mb-2">测试场景 3: 检查缓存机制</h2>
        <button 
          @click="testCacheFlow"
          class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          测试缓存流程
        </button>
      </div>
    </div>
    
    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-2">测试结果</h2>
      <div class="bg-gray-100 p-4 rounded">
        <pre>{{ testResults }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const testResults = ref('')

// 模拟 Scholar 比较数据
const mockScholarCompareData = {
  researcher1: {
    name: 'John Smith',
    scholarId: 'SCHOLAR123',
    scholar_id: 'SCHOLAR123',
    affiliation: 'MIT',
    avatar: '/image/avator.png',
    research_interests: ['Machine Learning', 'AI'],
    total_citations: 1000,
    h_index: 25
  },
  researcher2: {
    name: 'Jane Doe', 
    scholarId: 'SCHOLAR456',
    scholar_id: 'SCHOLAR456',
    affiliation: 'Stanford',
    avatar: '/image/avator.png',
    research_interests: ['Computer Vision', 'Deep Learning'],
    total_citations: 800,
    h_index: 20
  }
}

const testTransitPageFlow = () => {
  testResults.value = '开始测试过境页面流程...\n'
  
  // 模拟缓存数据
  if (import.meta.client) {
    sessionStorage.setItem('scholarCompareResult', JSON.stringify(mockScholarCompareData))
    testResults.value += '✓ 模拟数据已缓存到 sessionStorage\n'
  }
  
  // 重定向到 Scholar_Compare 页面，模拟从 Compare 页面的重定向
  router.push({
    path: '/scholar_compare',
    query: {
      user1: 'SCHOLAR123',
      user2: 'SCHOLAR456', 
      from: 'compare'
    }
  })
  
  testResults.value += '✓ 重定向到 Scholar_Compare 页面 (from=compare)\n'
}

const testDirectAccess = () => {
  testResults.value = '开始测试直接访问...\n'
  
  // 清除缓存
  if (import.meta.client) {
    sessionStorage.removeItem('scholarCompareResult')
    testResults.value += '✓ 清除缓存\n'
  }
  
  // 直接访问 Scholar_Compare 页面
  router.push({
    path: '/scholar_compare',
    query: {
      user1: 'SCHOLAR123',
      user2: 'SCHOLAR456'
    }
  })
  
  testResults.value += '✓ 直接访问 Scholar_Compare 页面\n'
}

const testCacheFlow = () => {
  testResults.value = '开始测试缓存流程...\n'
  
  if (import.meta.client) {
    // 设置缓存
    sessionStorage.setItem('scholarCompareResult', JSON.stringify(mockScholarCompareData))
    testResults.value += '✓ 设置缓存数据\n'
    
    // 检查缓存
    const cached = sessionStorage.getItem('scholarCompareResult')
    if (cached) {
      const data = JSON.parse(cached)
      testResults.value += `✓ 缓存数据验证成功: ${data.researcher1.name} vs ${data.researcher2.name}\n`
    }
    
    // 清除缓存
    sessionStorage.removeItem('scholarCompareResult')
    testResults.value += '✓ 缓存已清除\n'
  }
}
</script>
